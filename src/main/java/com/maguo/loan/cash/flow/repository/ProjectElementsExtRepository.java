package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.common.ProjectElementsExt;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * 项目要素扩展Repository
 *
 * @Author: Lior
 * @CreateTime: 2025/8/18 15:01
 */
public interface ProjectElementsExtRepository extends JpaRepository<ProjectElementsExt, String> {

    /**
     * 根据项目编码查询项目要素扩展
     * @param projectCode 项目编码
     * @return 项目要素扩展
     */
    ProjectElementsExt findByProjectCode(String projectCode);
}
