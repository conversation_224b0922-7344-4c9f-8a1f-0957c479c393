package com.maguo.loan.cash.flow.entrance.common.service;

import com.maguo.loan.cash.flow.entity.common.ProjectElements;
import com.maguo.loan.cash.flow.entity.common.ProjectElementsExt;
import com.maguo.loan.cash.flow.entity.vo.ProjectInfoVO;
import com.maguo.loan.cash.flow.enums.AbleStatus;
import com.maguo.loan.cash.flow.enums.ProjectDurationType;
import com.maguo.loan.cash.flow.repository.ProjectInfoRepository;
import com.maguo.loan.cash.flow.repository.ProjectElementsRepository;
import com.maguo.loan.cash.flow.repository.ProjectElementsExtRepository;
import com.maguo.loan.cash.flow.service.CacheService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目相关服务
 *
 * @Author: Lior
 * @CreateTime: 2025/8/18 14:11
 */
@Service
public class ProjectInfoService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectInfoService.class);

    @Autowired
    private ProjectInfoRepository projectInfoRepository;

    @Autowired
    private ProjectElementsRepository projectElementsRepository;

    @Autowired
    private ProjectElementsExtRepository projectElementsExtRepository;

    @Autowired
    private CacheService cacheService;

    /**
     * 项目信息缓存key前缀
     */
    private static final String PROJECT_INFO_CACHE_PREFIX = "PROJECT_INFO_";

    /**
     * 默认缓存过期时间：24 * 7小时
     */
    private static final Duration CACHE_DURATION = Duration.ofHours(24 * 7);

    /**
     * 根据项目编码查询项目完整信息（带缓存）
     *
     * @param projectCode 项目编码
     * @return 项目完整信息VO
     */
    public ProjectInfoVO queryProjectInfo(String projectCode) {
        if (StringUtils.isBlank(projectCode)) {
            logger.warn("项目编码为空，无法查询项目信息");
            return null;
        }

        // 构建缓存key
        String cacheKey = PROJECT_INFO_CACHE_PREFIX + projectCode;

        try {
            // 1. 先从缓存中获取
            Object cachedData = cacheService.get(cacheKey);
            if (cachedData instanceof ProjectInfoVO) {
                logger.info("从缓存中获取到项目信息，projectCode: {}", projectCode);
                return (ProjectInfoVO) cachedData;
            }

            // 2. 缓存中没有，从数据库查询
            ProjectInfoVO vo = queryProjectInfoFromDatabase(projectCode);

            // 3. 查询结果放入缓存，如果是临时配置则使用临时配置的过期时间
            if (vo != null) {
                Duration cacheDuration = calculateCacheDuration(vo.getElements());
                cacheService.put(cacheKey, vo, cacheDuration);
                logger.info("项目信息已放入缓存，projectCode: {}, 缓存时长: {}秒", projectCode, cacheDuration.getSeconds());
            }

            return vo;

        } catch (Exception e) {
            logger.error("查询项目完整信息异常，projectCode: {}", projectCode, e);
            throw new RuntimeException("查询项目信息失败", e);
        }
    }

    /**
     * 从数据库查询项目完整信息
     *
     * @param projectCode 项目编码
     * @return 项目完整信息VO
     */
    private ProjectInfoVO queryProjectInfoFromDatabase(String projectCode) {
        logger.info("从数据库查询项目完整信息，projectCode: {}", projectCode);

        // 1. 查询项目基本信息，只查询状态为启用的项目
        ProjectInfoVO vo = projectInfoRepository.findByProjectCode(projectCode);
        if (vo == null) {
            logger.info("未找到项目信息，projectCode: {}", projectCode);
            return null;
        }

        // 2. 查询项目要素
        // 先看有没有临时的，如果有则使用临时，否则使用长期的。
        // 临时的结束时间需要放在缓存的过期时间中
        ProjectElements elements = queryProjectElements(projectCode);
        if (elements != null) {
            vo.setElements(elements);
            logger.info("查询到项目要素信息，projectCode: {}, 时效类型: {}", projectCode, elements.getProjectDurationType());
        } else {
            logger.info("未找到项目要素信息，projectCode: {}", projectCode);
        }

        // 3. 查询项目要素扩展
        ProjectElementsExt elementsExt = projectElementsExtRepository.findByProjectCode(projectCode);
        if (elementsExt != null) {
            vo.setElementsExt(elementsExt);
            logger.info("查询到项目要素扩展信息，projectCode: {}", projectCode);
        } else {
            logger.info("未找到项目要素扩展信息，projectCode: {}", projectCode);
        }

        logger.info("项目完整信息查询完成，projectCode: {}", projectCode);
        return vo;
    }

    /**
     * 清除项目信息缓存
     *
     * @param projectCode 项目编码
     */
    public void clearProjectInfoCache(String projectCode) {
        if (StringUtils.isBlank(projectCode)) {
            logger.warn("项目编码为空，无法清除缓存");
            return;
        }

        String cacheKey = PROJECT_INFO_CACHE_PREFIX + projectCode;
        try {
            cacheService.delete(cacheKey);
            logger.info("已清除项目信息缓存，projectCode: {}", projectCode);
        } catch (Exception e) {
            logger.error("清除项目信息缓存异常，projectCode: {}", projectCode, e);
        }
    }

    /**
     * 批量清除项目信息缓存
     *
     * @param projectCodes 项目编码列表
     */
    public void clearProjectInfoCache(List<String> projectCodes) {
        if (projectCodes == null || projectCodes.isEmpty()) {
            logger.warn("项目编码列表为空，无法清除缓存");
            return;
        }

        for (String projectCode : projectCodes) {
            clearProjectInfoCache(projectCode);
        }
    }

    /**
     * 查询项目要素，优先查询临时配置，如果没有或已过期则查询长期配置
     *
     * @param projectCode 项目编码
     * @return 项目要素
     */
    private ProjectElements queryProjectElements(String projectCode) {
        LocalDateTime currentTime = LocalDateTime.now();

        // 1. 先查询有效的临时项目要素
        ProjectElements temporaryElements = projectElementsRepository.findValidTemporaryElements(
            projectCode, AbleStatus.ENABLE, ProjectDurationType.TEMPORARY, currentTime);

        if (temporaryElements != null) {
            logger.info("查询到有效的临时项目要素，projectCode: {}, 有效期: {} - {}",
                projectCode, temporaryElements.getTempStartTime(), temporaryElements.getTempEndTime());
            return temporaryElements;
        }

        // 2. 没有有效的临时配置，查询长期配置
        ProjectElements longtimeElements = projectElementsRepository.findByProjectCodeAndEnabledAndProjectDurationType(
            projectCode, AbleStatus.ENABLE, ProjectDurationType.LONGTIME);

        if (longtimeElements != null) {
            logger.info("查询到长期项目要素，projectCode: {}", projectCode);
            return longtimeElements;
        }

        logger.warn("未找到任何有效的项目要素配置，projectCode: {}", projectCode);
        return null;
    }

    /**
     * 计算缓存时长，如果是临时配置则使用临时配置的结束时间，否则使用默认时长
     *
     * @param elements 项目要素
     * @return 缓存时长
     */
    private Duration calculateCacheDuration(ProjectElements elements) {
        if (elements == null) {
            return CACHE_DURATION;
        }

        // 如果是临时配置且有结束时间，计算到结束时间的时长
        if (ProjectDurationType.TEMPORARY.equals(elements.getProjectDurationType())
            && elements.getTempEndTime() != null) {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime endTime = elements.getTempEndTime();

            if (endTime.isAfter(now)) {
                return Duration.between(now, endTime);
            }
        }

        // 长期配置或临时配置已过期，使用默认缓存时长
        return CACHE_DURATION;
    }

}
