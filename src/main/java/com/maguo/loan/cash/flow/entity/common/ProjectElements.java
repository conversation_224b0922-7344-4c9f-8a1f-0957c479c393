package com.maguo.loan.cash.flow.entity.common;

import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.entity.BaseEntity;
import com.maguo.loan.cash.flow.enums.AbleStatus;
import com.maguo.loan.cash.flow.enums.ProjectDurationType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 产品要素表-主表
 *
 * @Author: Lior
 * @CreateTime: 2025/8/18 11:55
 */
@Getter
@Setter
@Entity
@Table(name = "project_elements")
public class ProjectElements extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1234567890123456789L;

    /**
     * 关联的项目唯一编码
     */
    @Column(name = "project_code")
    private String projectCode;

    /**
     * 可提现范围(元) (格式 如 1000-50000)
     */
    @Column(name = "drawable_amount_range")
    private String drawableAmountRange;

    /**
     * 单笔提现步长(元)
     */
    @Column(name = "drawable_amount_step")
    private String drawableAmountStep;

    /**
     * 授信黑暗期 (格式 HH:mm-HH:mm)
     */
    @Column(name = "credit_dark_hours")
    private String creditDarkHours;

    /**
     * 用信黑暗期 (格式 HH:mm-HH:mm)
     */
    @Column(name = "loan_dark_hours")
    private String loanDarkHours;

    /**
     * 还款黑暗期 (格式 HH:mm-HH:mm)
     */
    @Column(name = "repay_dark_hours")
    private String repayDarkHours;

    /**
     * 资金方授信黑暗期
     */
    @Column(name = "funding_credit_dark_hours")
    private String fundingCreditDarkHours;

    /**
     * 资金方用信黑暗期
     */
    @Column(name = "funding_loan_dark_hours")
    private String fundingLoanDarkHours;

    /**
     * 资金方还款黑暗期
     */
    @Column(name = "funding_repay_dark_hours")
    private String fundingRepayDarkHours;

    /**
     * 日授信限额(万元)
     */
    @Column(name = "daily_credit_limit")
    private BigDecimal dailyCreditLimit;

    /**
     * 日放款限额(万元)
     */
    @Column(name = "daily_loan_limit")
    private BigDecimal dailyLoanLimit;

    /**
     * 授信锁定期限(天)
     */
    @Column(name = "credit_lock_days")
    private Integer creditLockDays;

    /**
     * 用信锁定期限(天)
     */
    @Column(name = "loan_lock_days")
    private Integer loanLockDays;

    /**
     * 对客利率(%)
     */
    @Column(name = "customer_interest_rate")
    private String customerInterestRate;

    /**
     * 对资利率(%)
     */
    @Column(name = "funding_interest_rate")
    private String fundingInterestRate;

    /**
     * 年龄范围(岁) (格式 如 22-55)
     */
    @Column(name = "age_range")
    private String ageRange;

    /**
     * 支持的还款类型 (英文逗号分隔)
     */
    @Column(name = "supported_repay_types")
    private String supportedRepayTypes;

    /**
     * 借款期限 (英文逗号分隔)
     */
    @Column(name = "loan_terms")
    private String loanTerms;

    /**
     * 资方路由
     */
    @Column(name = "capital_route")
    private String capitalRoute;

    /**
     * 项目时效类型（LONGTIME, TEMPORARY）
     */
    @Column(name = "project_duration_type")
    @Enumerated(EnumType.STRING)
    private ProjectDurationType projectDurationType;

    /**
     * 临时配置有效期起
     */
    @Column(name = "temp_start_time")
    private LocalDateTime tempStartTime;

    /**
     * 临时配置有效期止
     */
    @Column(name = "temp_end_time")
    private LocalDateTime tempEndTime;

    /**
     * 启用状态
     */
    @Enumerated(EnumType.STRING)
    private AbleStatus enabled;

    /**
     * 年结是否顺延
     */
    @Column(name = "grace_next")
    private String graceNext;

    @Override
    protected String prefix() {
        return "PE";
    }

    @Override
    public void prePersist() {
        if (StringUtil.isBlank(super.getId())) {
            setId(genId());
        }
        super.setCreatedBy("sys");
        super.setCreatedTime(LocalDateTime.now());
    }
}
