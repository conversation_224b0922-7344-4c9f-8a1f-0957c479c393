package com.maguo.loan.cash.flow.entity;

import com.maguo.loan.cash.flow.enums.ApplyType;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

@Entity
@Table(name = "risk_decision_flow_config")
public class RiskDecisionFlowConfig extends BaseEntity {
    //决策流编号
    private String decisionFlowNo;

    //密钥
    private String secretKey;

    //阶段
    @Enumerated(EnumType.STRING)
    private ApplyType stage;

    //项目唯一编码
    private String projectCode;

    public String getDecisionFlowNo() {
        return decisionFlowNo;
    }

    public void setDecisionFlowNo(String decisionFlowNo) {
        this.decisionFlowNo = decisionFlowNo;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public ApplyType getStage() {
        return stage;
    }

    public void setStage(ApplyType stage) {
        this.stage = stage;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }
}
