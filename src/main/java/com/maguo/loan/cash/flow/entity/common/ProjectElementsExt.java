package com.maguo.loan.cash.flow.entity.common;

import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 产品要素表-扩展表 (含冗余字段)
 *
 * @Author: Lior
 * @CreateTime: 2025/8/18 11:56
 */
@Getter
@Setter
@Entity
@Table(name = "project_elements_ext")
public class ProjectElementsExt extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1234567890123456789L;

    /**
     * 关联的项目唯一编码
     */
    @Column(name = "project_code")
    private String projectCode;

    /**
     * 年利率基数(天) (如 360或365)
     */
    @Column(name = "interest_days_basis")
    private String interestDaysBasis;

    /**
     * 是否支持线下跨日还款
     */
    @Column(name = "allow_cross_day_repay")
    private String allowCrossDayRepay;

    /**
     * 风控模型渠道
     */
    @Column(name = "risk_model_channel")
    private String riskModelChannel;

    /**
     * 放款支付渠道
     */
    @Column(name = "loan_payment_channel")
    private String loanPaymentChannel;

    /**
     * 扣款绑卡渠道
     */
    @Column(name = "deduction_bind_card_channel")
    private String deductionBindCardChannel;

    /**
     * 扣款商户号
     */
    @Column(name = "deduction_merchant_code")
    private String deductionMerchantCode;

    /**
     * 签章渠道
     */
    @Column(name = "sign_channel")
    private String signChannel;

    /**
     * 逾期短信发送方
     */
    @Column(name = "overdue_sms_sender")
    private String overdueSmsSender;

    /**
     * 短信渠道
     */
    @Column(name = "sms_channel")
    private String smsChannel;

    /**
     * 逾期宽限期类型 (SQ:首期, MQ:每期)
     */
    @Column(name = "grace_period_type")
    private String gracePeriodType;

    /**
     * 逾期宽限期(天)
     */
    @Column(name = "grace_period_days")
    private String gracePeriodDays;

    /**
     * 节假日是否顺延
     */
    @Column(name = "holiday_postpone")
    private String holidayPostpone;

    /**
     * 征信查询方
     */
    @Column(name = "credit_query_party")
    private String creditQueryParty;

    /**
     * 征信上报方
     */
    @Column(name = "credit_report_sender")
    private String creditReportSender;

    /**
     * 催收方
     */
    @Column(name = "collection_party")
    private String collectionParty;

    /**
     * 是否推送催收数据
     */
    @Column(name = "push_collection_data")
    private String pushCollectionData;

    /**
     * 是否支持催收减免
     */
    @Column(name = "allow_collection_waiver")
    private String allowCollectionWaiver;

    @Override
    protected String prefix() {
        return "PEE";
    }

    @Override
    public void prePersist() {
        if (StringUtil.isBlank(super.getId())) {
            setId(genId());
        }
        super.setCreatedBy("sys");
        super.setCreatedTime(LocalDateTime.now());
    }
}
