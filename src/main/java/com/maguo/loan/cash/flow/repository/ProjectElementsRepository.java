package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.common.ProjectElements;
import com.maguo.loan.cash.flow.enums.AbleStatus;
import com.maguo.loan.cash.flow.enums.ProjectDurationType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目要素Repository
 *
 * @Author: Lior
 * @CreateTime: 2025/8/18 15:00
 */
public interface ProjectElementsRepository extends JpaRepository<ProjectElements, String> {

    /**
     * 根据项目编码查询项目要素
     * @param projectCode 项目编码
     * @return 项目要素
     */
    ProjectElements findByProjectCode(String projectCode);

    /**
     * 根据项目编码查询启用状态的项目要素，按时效类型排序（临时优先）
     * @param projectCode 项目编码
     * @param enabled 启用状态
     * @return 项目要素列表
     */
    List<ProjectElements> findByProjectCodeAndEnabledOrderByProjectDurationTypeDesc(String projectCode, AbleStatus enabled);

    /**
     * 根据项目编码查询有效的临时项目要素
     * @param projectCode 项目编码
     * @param enabled 启用状态
     * @param durationType 时效类型
     * @param currentTime 当前时间
     * @return 项目要素
     */
    @Query("SELECT pe FROM ProjectElements pe WHERE pe.projectCode = :projectCode " +
           "AND pe.enabled = :enabled " +
           "AND pe.projectDurationType = :durationType " +
           "AND pe.tempStartTime <= :currentTime " +
           "AND pe.tempEndTime >= :currentTime")
    ProjectElements findValidTemporaryElements(@Param("projectCode") String projectCode,
                                             @Param("enabled") AbleStatus enabled,
                                             @Param("durationType") ProjectDurationType durationType,
                                             @Param("currentTime") LocalDateTime currentTime);

    /**
     * 根据项目编码查询长期项目要素
     * @param projectCode 项目编码
     * @param enabled 启用状态
     * @param durationType 时效类型
     * @return 项目要素
     */
    ProjectElements findByProjectCodeAndEnabledAndProjectDurationType(String projectCode,
                                                                    AbleStatus enabled,
                                                                    ProjectDurationType durationType);
}
