package com.maguo.loan.cash.flow.entity.common;

import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.entity.BaseEntity;
import com.maguo.loan.cash.flow.enums.AbleStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 项目信息核心表
 *
 * @Author: Lior
 * @CreateTime: 2025/8/18 11:57
 */
@Getter
@Setter
@Entity
@Table(name = "project_info")
public class ProjectInfo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1234567890123456789L;

    /**
     * 项目唯一编码
     */
    @Column(name = "project_code")
    private String projectCode;

    /**
     * 项目名称
     */
    @Column(name = "project_name")
    private String projectName;

    /**
     * 资产方编码 (关联资产方表)
     */
    @Column(name = "flow_channel")
    private String flowChannel;

    /**
     * 融担方编码 (关联融担方表)
     */
    @Column(name = "guarantee_code")
    private String guaranteeCode;

    /**
     * 资金方编码 (关联资金方表)
     */
    @Column(name = "capital_channel")
    private String capitalChannel;

    /**
     * 项目类型编码 (关联项目类型表)
     */
    @Column(name = "project_type_code")
    private String projectTypeCode;

    /**
     * 项目状态
     */
    @Enumerated(EnumType.STRING)
    private AbleStatus enabled;

    /**
     * 项目开始日期
     */
    @Column(name = "start_date")
    private LocalDate startDate;

    /**
     * 项目结束日期
     */
    @Column(name = "end_date")
    private LocalDate endDate;
    @Override
    protected String prefix() {
        return "PI";
    }

    @Override
    public void prePersist() {
        if (StringUtil.isBlank(super.getId())) {
            setId(genId());
        }
        super.setCreatedBy("sys");
        super.setCreatedTime(LocalDateTime.now());
    }
}
