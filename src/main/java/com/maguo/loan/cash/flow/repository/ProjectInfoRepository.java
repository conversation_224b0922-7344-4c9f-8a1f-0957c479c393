package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.common.ProjectInfo;
import com.maguo.loan.cash.flow.entity.vo.ProjectInfoVO;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * 项目信息
 *
 * @Author: Lior
 * @CreateTime: 2025/8/18 14:20
 */
public interface ProjectInfoRepository extends JpaRepository<ProjectInfo, String> {

    ProjectInfoVO findByProjectCode(String projectCode);
}
