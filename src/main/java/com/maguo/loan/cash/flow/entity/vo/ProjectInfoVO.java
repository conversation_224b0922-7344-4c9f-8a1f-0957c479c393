package com.maguo.loan.cash.flow.entity.vo;

import com.maguo.loan.cash.flow.entity.common.ProjectContractCapital;
import com.maguo.loan.cash.flow.entity.common.ProjectContractFlow;
import com.maguo.loan.cash.flow.entity.common.ProjectElements;
import com.maguo.loan.cash.flow.entity.common.ProjectElementsExt;
import com.maguo.loan.cash.flow.enums.AbleStatus;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * 项目信息VO
 *
 * @Author: Lior
 * @CreateTime: 2025/8/18 14:47
 */
@Getter
@Setter
public class ProjectInfoVO {

    /**
     * 项目唯一编码
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 资产方编码 (关联资产方表)
     */
    private String flowChannel;

    /**
     * 融担方编码 (关联融担方表)
     */
    private String guaranteeCode;

    /**
     * 资金方编码 (关联资金方表)
     */
    private String capitalChannel;

    /**
     * 项目类型编码 (关联项目类型表)
     */
    private String projectTypeCode;

    /**
     * 项目状态
     */
    @Enumerated(EnumType.STRING)
    private AbleStatus enabled;

    /**
     * 项目开始日期
     */
    private LocalDate startDate;

    /**
     * 项目结束日期
     */
    private LocalDate endDate;

    /**
     * 项目要素
     */
    private ProjectElements elements;

    /**
     * 项目要素扩展
     */
    private ProjectElementsExt elementsExt;

    /**
     * 项目资金合同
     */
    private ProjectContractCapital contractCapital;

    /**
     * 项目资产合同
     */
    private ProjectContractFlow contractFlow;

}
